/* Abridged user-agent-style defaults (common across modern browsers) */

/* Document */
html { line-height: 1.15; -webkit-text-size-adjust: 100%; }
body { margin: 8px; }

/* Sections */
main, article, aside, nav, section, header, footer { display: block; }

/* Headings & text */
h1 { font-size: 2em; margin: .67em 0; }
h2 { font-size: 1.5em; margin: .75em 0; }
h3 { font-size: 1.17em; margin: .83em 0; }
h4 { font-size: 1em;   margin: 1.12em 0; }
h5 { font-size: .83em; margin: 1.5em 0; }
h6 { font-size: .67em; margin: 1.67em 0; }

p, pre, blockquote, figure { margin: 1em 0; }
hr { box-sizing: content-box; height: 0; overflow: visible; }

b, strong { font-weight: bolder; }
small { font-size: 80%; }
sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub { bottom: -0.25em; }
sup { top: -0.5em; }

code, kbd, samp, pre { font-family: monospace, monospace; font-size: 1em; }

/* Links */
a { background-color: transparent; text-decoration: underline; color: -webkit-link; cursor: pointer; }

/* Lists */
ul, ol { margin: 1em 0; padding-left: 40px; }
ul { list-style: disc outside; }
ol { list-style: decimal outside; }
dl { margin: 1em 0; }
dd { margin-left: 40px; }

/* Embedded content */
img { border-style: none; max-width: 100%; }
svg:not(:root) { overflow: hidden; }

/* Tables */
table { border-collapse: separate; border-spacing: 2px; }
thead, tbody, tfoot, tr { border-color: inherit; }
th { font-weight: bold; }
td, th { padding: 1px; }


/* Forms */
button, input, select, textarea { font: inherit; margin: 0; }
button, input { overflow: visible; }
button, select { text-transform: none; }
button, [type="button"], [type="reset"], [type="submit"] {
  -webkit-appearance: button;
  appearance: button;
}
fieldset { padding: .35em .75em .625em; margin: 0 2px; border: 1px solid #c0c0c0; }
legend { box-sizing: border-box; color: inherit; display: table; max-width: 100%; padding: 0; white-space: normal; }
textarea { overflow: auto; }
[type="search"] {
  -webkit-appearance: textfield;
  appearance: textfield;
  outline-offset: -2px;
}
[type="search"]::-webkit-search-decoration { -webkit-appearance: none; }
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button { height: auto; }
[type="checkbox"], [type="radio"] { box-sizing: border-box; padding: 0; }
::-webkit-file-upload-button { -webkit-appearance: button; font: inherit; }

/* Interactive */
details { display: block; }
summary { display: list-item; }

/* Misc */
template { display: none; }
[hidden] { display: none !important; }
